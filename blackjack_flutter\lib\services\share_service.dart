import 'package:share_plus/share_plus.dart';

class ShareService {
  static const String _appName = 'Blackjack by <PERSON><PERSON><PERSON>';
  static const String _appDescription = 'Professional Blackjack Game - Master Your 21 Skills';
  
  static final List<String> _shareMessages = [
    '🎰 Experience the thrill of Vegas with $_appName! Master your blackjack skills in this professional casino game! ♠️♥️',
    '🃏 Join millions playing $_appName - the most realistic blackjack experience on mobile! Test your strategy now! 🎯',
    '♠️ Beat the dealer in $_appName! Professional blackjack with authentic casino rules and stunning graphics! 🏆',
    '🎲 Ready to master 21? $_appName brings Vegas-style blackjack to your fingertips! Play like a pro! 💎',
    '🌟 Discover $_appName - where strategy meets excitement! The ultimate mobile blackjack experience awaits! 🎰',
    '💰 Think you can beat the house? Challenge yourself with $_appName - professional blackjack at its finest! ♠️',
    '🎯 Master the art of 21 with $_appName! Realistic gameplay, professional features, endless entertainment! 🃏',
    '🏆 $_appName sets the gold standard for mobile blackjack! Experience casino-quality gaming anywhere! 🎰♠️'
  ];

  static String _getRandomShareMessage() {
    final random = DateTime.now().millisecondsSinceEpoch % _shareMessages.length;
    return _shareMessages[random];
  }

  static Future<void> shareGame({String? customMessage}) async {
    try {
      final message = customMessage ?? _getRandomShareMessage();
      
      await Share.share(
        message,
        subject: _appDescription,
      );
    } catch (e) {
      // 分享失败时的处理
      print('Share failed: $e');
    }
  }

  static Future<void> shareScore({
    required double balance,
    required int gamesPlayed,
    required int gamesWon,
  }) async {
    try {
      final winRate = gamesPlayed > 0 ? (gamesWon / gamesPlayed * 100).toStringAsFixed(1) : '0.0';
      
      final message = '''🎰 Check out my $_appName stats! 🎰

💰 Current Balance: \$${balance.toInt()}
🎯 Games Played: $gamesPlayed
🏆 Games Won: $gamesWon
📊 Win Rate: $winRate%

Think you can beat my record? Download $_appName and test your blackjack skills! ♠️♥️♦️♣️''';

      await Share.share(
        message,
        subject: 'My $_appName Statistics',
      );
    } catch (e) {
      print('Share score failed: $e');
    }
  }

  static Future<void> shareAchievement({
    required String achievement,
    required double balance,
  }) async {
    try {
      final message = '''🏆 Achievement Unlocked in $_appName! 🏆

🎯 $achievement
💰 Current Balance: \$${balance.toInt()}

Join me in this amazing blackjack adventure! Master your skills and beat the dealer! ♠️🎰''';

      await Share.share(
        message,
        subject: 'Achievement in $_appName',
      );
    } catch (e) {
      print('Share achievement failed: $e');
    }
  }
}
