# 分享功能说明

## 功能概述

本项目已成功集成了手机系统原生分享功能，用户可以通过分享按钮将游戏内容分享到各种社交平台和应用。

## 实现的功能

### 1. 分享按钮
- 位置：游戏界面右上角
- 图标：分享图标 (Icons.share)
- 功能：点击后弹出分享选项菜单

### 2. 分享选项
点击分享按钮后，会弹出一个底部弹窗，包含以下选项：

#### 分享游戏 (Share Game)
- 功能：分享游戏本身，邀请朋友一起玩
- 内容：包含游戏介绍和推荐文案
- 文案示例：
  - "🎰 Experience the thrill of Vegas with Blackjack by <PERSON>assor! Master your blackjack skills in this professional casino game! ♠️♥️"
  - "🃏 Join millions playing Blackjack by Fassor - the most realistic blackjack experience on mobile! Test your strategy now! 🎯"

#### 分享统计数据 (Share My Stats)
- 功能：分享个人游戏统计数据
- 包含信息：
  - 当前余额
  - 游戏局数
  - 胜利局数
  - 胜率
- 文案示例：
  ```
  🎰 Check out my Blackjack by Fassor stats! 🎰
  
  💰 Current Balance: $1,250
  🎯 Games Played: 15
  🏆 Games Won: 9
  📊 Win Rate: 60.0%
  
  Think you can beat my record? Download Blackjack by Fassor and test your blackjack skills! ♠️♥️♦️♣️
  ```

## 技术实现

### 依赖包
- `share_plus: ^7.2.2` - Flutter官方推荐的分享插件

### 核心文件

#### 1. ShareService (`lib/services/share_service.dart`)
- 分享服务类，处理所有分享相关逻辑
- 包含多种预设分享文案
- 支持分享游戏、统计数据、成就等

#### 2. TopArea Widget (`lib/widgets/top_area.dart`)
- 更新了分享按钮功能
- 添加了分享选项弹窗
- 集成了GameProvider统计数据

#### 3. GameProvider (`lib/providers/game_provider.dart`)
- 添加了游戏统计数据跟踪
- 包含：游戏局数、胜利局数、总赢利、总损失
- 自动计算胜率

### 统计数据跟踪
系统会自动跟踪以下数据：
- `gamesPlayed`: 总游戏局数
- `gamesWon`: 胜利局数
- `totalWinnings`: 总赢利金额
- `totalLosses`: 总损失金额
- `winRate`: 胜率百分比

## 使用方法

1. 点击游戏界面右上角的分享按钮
2. 选择分享类型：
   - "Share Game" - 分享游戏给朋友
   - "Share My Stats" - 分享个人统计数据
3. 系统会调用手机原生分享功能
4. 用户可以选择分享到微信、微博、短信、邮件等任何支持的应用

## 分享内容特点

- **多样化文案**：每次分享会随机选择不同的推广文案
- **Emoji丰富**：使用大量emoji让分享内容更生动
- **SEO优化**：文案包含关键词，有利于搜索和传播
- **专业感**：突出游戏的专业性和赌场体验
- **社交化**：鼓励用户挑战和比较

## 兼容性

- ✅ iOS：支持系统原生分享功能
- ✅ Android：支持系统原生分享功能
- ✅ 支持所有主流社交应用：微信、微博、QQ、短信、邮件等

## 注意事项

1. 分享功能需要在真机上测试，模拟器可能无法完全展示分享选项
2. 不同手机系统的分享界面可能略有不同
3. 分享内容会根据目标应用自动调整格式
