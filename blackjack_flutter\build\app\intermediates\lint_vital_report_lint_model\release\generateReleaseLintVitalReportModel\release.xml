<variant
    name="release"
    package="com.example.blackjack_flutter"
    minSdkVersion="21"
    targetSdkVersion="35"
    shrinking="true"
    mergedManifest="D:\code\appweb\blackjack_flutter\build\app\intermediates\merged_manifest\release\processReleaseMainManifest\AndroidManifest.xml"
    manifestMergeReport="D:\code\appweb\blackjack_flutter\build\app\outputs\logs\manifest-merger-release-report.txt"
    proguardFiles="D:\code\appweb\blackjack_flutter\build\app\intermediates\default_proguard_files\global\proguard-android-optimize.txt-8.7.3;D:\flutter\flutter\packages\flutter_tools\gradle\flutter_proguard_rules.pro;proguard-rules.pro"
    partialResultsDir="D:\code\appweb\blackjack_flutter\build\app\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out">
  <buildFeatures
      namespacing="REQUIRED"/>
  <sourceProviders>
    <sourceProvider
        manifests="src\main\AndroidManifest.xml"
        javaDirectories="src\main\java;src\release\java;src\main\kotlin;src\release\kotlin"
        resDirectories="src\main\res;src\release\res"
        assetsDirectories="src\main\assets;src\release\assets"/>
  </sourceProviders>
  <testSourceProviders>
  </testSourceProviders>
  <testFixturesSourceProviders>
  </testFixturesSourceProviders>
  <manifestPlaceholders>
    <placeholder
        name="applicationName"
        value="android.app.Application" />
  </manifestPlaceholders>
  <artifact
      classOutputs="D:\code\appweb\blackjack_flutter\build\app\intermediates\javac\release\compileReleaseJavaWithJavac\classes;D:\code\appweb\blackjack_flutter\build\app\tmp\kotlin-classes\release;D:\code\appweb\blackjack_flutter\build\app\kotlinToolingMetadata;D:\code\appweb\blackjack_flutter\build\app\intermediates\compile_and_runtime_not_namespaced_r_class_jar\release\processReleaseResources\R.jar"
      type="MAIN"
      applicationId="com.example.blackjack_flutter"
      generatedSourceFolders="D:\code\appweb\blackjack_flutter\build\app\generated\ap_generated_sources\release\out"
      generatedResourceFolders="D:\code\appweb\blackjack_flutter\build\app\generated\res\resValues\release"
      desugaredMethodsFiles="C:\Users\<USER>\.gradle\caches\8.12\transforms\228bfc3f315fa1875616020ef0ac12d6\transformed\D8BackportedDesugaredMethods.txt">
  </artifact>
</variant>
