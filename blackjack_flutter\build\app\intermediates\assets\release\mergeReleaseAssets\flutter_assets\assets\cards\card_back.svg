<?xml version="1.0" encoding="UTF-8"?>
<svg width="60" height="90" viewBox="0 0 60 90" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="cardBackGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1e40af;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:1" />
    </linearGradient>
    <pattern id="cardPattern" x="0" y="0" width="8" height="8" patternUnits="userSpaceOnUse">
      <rect width="8" height="8" fill="none"/>
      <path d="M0,4 L4,0 M4,8 L8,4" stroke="rgba(255,255,255,0.2)" stroke-width="1"/>
    </pattern>
  </defs>
  
  <!-- Card background -->
  <rect x="2" y="2" width="56" height="86" rx="6" ry="6" fill="url(#cardBackGradient)" stroke="#000" stroke-width="2"/>
  
  <!-- Pattern overlay -->
  <rect x="4" y="4" width="52" height="82" rx="4" ry="4" fill="url(#cardPattern)"/>
  
  <!-- Center design -->
  <rect x="20" y="35" width="20" height="20" rx="2" ry="2" fill="none" stroke="rgba(255,255,255,0.4)" stroke-width="2"/>
  <text x="30" y="48" text-anchor="middle" fill="white" font-family="serif" font-size="12" font-weight="bold">♠♥♦♣</text>
</svg>
