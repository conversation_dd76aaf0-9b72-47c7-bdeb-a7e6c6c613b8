import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/game_provider.dart';
import 'animated_card.dart';

class SplitHandArea extends StatelessWidget {
  const SplitHandArea({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<GameProvider>(
      builder: (context, gameProvider, child) {
        if (!gameProvider.isSplitGame || gameProvider.splitHands.isEmpty) {
          return const SizedBox.shrink();
        }

        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4), // 减少垂直padding：8 -> 4
          child: Column(
            children: [
              // 水平排列的两组牌
              Row(
                children: [
                  for (int i = 0; i < gameProvider.splitHands.length; i++)
                    Expanded(
                      child: _buildSplitHand(gameProvider, i),
                    ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildSplitHand(GameProvider gameProvider, int handIndex) {
    final hand = gameProvider.splitHands[handIndex];
    final isCurrentHand = gameProvider.currentHandIndex == handIndex;
    final isCompleted = hand.isComplete || hand.isBust;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8),
      child: Column(
        children: [
          // 简化的手牌标题
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: isCurrentHand && !isCompleted
                  ? Colors.orange.shade600
                  : isCompleted
                      ? Colors.green.shade600
                      : Colors.grey.shade600,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              'Hand ${handIndex + 1}',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),

          const SizedBox(height: 4), // 减少间距：8 -> 4

          // 分数显示
          if (hand.cards.isNotEmpty)
            Text(
              '${hand.score}',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.bold,
                shadows: [
                  Shadow(
                    color: Colors.black,
                    blurRadius: 2,
                    offset: Offset(1, 1),
                  ),
                ],
              ),
            ),

          const SizedBox(height: 4), // 减少间距：8 -> 4

          // 简化的手牌区域 - 去掉白色框
          Container(
            height: 100, // 调整高度以适应更大的分牌卡牌
            child: hand.cards.isEmpty
                ? const SizedBox.shrink()
                : _buildStackedCards(hand.cards),
          ),

          const SizedBox(height: 4), // 减少间距：8 -> 4

          // 下注显示
          Text(
            'Bet: ${hand.bet.toStringAsFixed(0)}',
            style: TextStyle(
              color: Colors.white,
              fontSize: 12,
              fontWeight: FontWeight.bold,
              shadows: [
                Shadow(
                  color: Colors.black,
                  blurRadius: 1,
                  offset: Offset(1, 1),
                ),
              ],
            ),
          ),

          // 状态指示器
          if (hand.isBust)
            Container(
              margin: const EdgeInsets.only(top: 4),
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: Colors.red.shade600,
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Text(
                'BUST',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                ),
              ),
            )
          else if (hand.isBlackjack)
            Container(
              margin: const EdgeInsets.only(top: 4),
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: Colors.amber.shade600,
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Text(
                'BJ',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildStackedCards(List<dynamic> cards) {
    // 动态计算分牌卡牌间距：由于分牌卡牌较小，间距也相应调整
    double getCardSpacing(int cardCount) {
      if (cardCount <= 2) return 35.0;      // 1-2张牌：正常间距
      if (cardCount == 3) return 30.0;      // 3张牌：稍微靠近
      if (cardCount == 4) return 26.0;      // 4张牌：更靠近
      if (cardCount == 5) return 22.0;      // 5张牌：很靠近
      return 18.0;                          // 6张及以上：最紧密
    }

    double spacing = getCardSpacing(cards.length);

    return Center(
      child: SizedBox(
        width: 60 + (cards.length - 1) * spacing, // 动态间距
        height: 90,
        child: Stack(
          children: cards
              .asMap()
              .entries
              .map((entry) => _buildCard(entry.value, entry.key, spacing))
              .toList(),
        ),
      ),
    );
  }

  Widget _buildCard(dynamic card, int cardIndex, double spacing) {
    String cardName;
    if (card is String) {
      cardName = card;
    } else {
      cardName = card.toString();
    }

    return AnimatedPositioned(
      duration: const Duration(milliseconds: 300), // 靠紧动画时长
      curve: Curves.easeInOut, // 平滑的动画曲线
      left: cardIndex * spacing, // 使用动态间距
      child: AnimatedCard(
        cardName: cardName,
        width: 60,
        height: 90,
        animationDelay: Duration(
          milliseconds: cardIndex < 2 ? cardIndex * 150 : 0, // 前两张牌有延迟，后续卡牌立即显示
        ),
        zIndex: cardIndex,
      ),
    );
  }
}
