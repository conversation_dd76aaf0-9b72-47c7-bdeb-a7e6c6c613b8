[{"merged": "io.flutter.plugins.pathprovider.path_provider_android-release-25:/layout-v21/notification_template_custom_big.xml", "source": "io.flutter.plugins.pathprovider.path_provider_android-core-1.13.1-7:/layout-v21/notification_template_custom_big.xml"}, {"merged": "io.flutter.plugins.pathprovider.path_provider_android-release-25:/layout-v21/notification_action.xml", "source": "io.flutter.plugins.pathprovider.path_provider_android-core-1.13.1-7:/layout-v21/notification_action.xml"}, {"merged": "io.flutter.plugins.pathprovider.path_provider_android-release-25:/layout-v21/notification_template_icon_group.xml", "source": "io.flutter.plugins.pathprovider.path_provider_android-core-1.13.1-7:/layout-v21/notification_template_icon_group.xml"}, {"merged": "io.flutter.plugins.pathprovider.path_provider_android-release-25:/layout-v21/notification_action_tombstone.xml", "source": "io.flutter.plugins.pathprovider.path_provider_android-core-1.13.1-7:/layout-v21/notification_action_tombstone.xml"}]