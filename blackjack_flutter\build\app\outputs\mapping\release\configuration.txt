# The proguard configuration file for the following section is D:\code\appweb\blackjack_flutter\build\app\intermediates\aapt_proguard_file\release\processReleaseResources\aapt_rules.txt
-keep class android.app.Application { <init>(); }
-keep class androidx.core.app.CoreComponentFactory { <init>(); }
-keep class androidx.profileinstaller.ProfileInstallReceiver { <init>(); }
-keep class androidx.startup.InitializationProvider { <init>(); }
-keep class com.example.blackjack_flutter.MainActivity { <init>(); }

# End of content from D:\code\appweb\blackjack_flutter\build\app\intermediates\aapt_proguard_file\release\processReleaseResources\aapt_rules.txt
# The proguard configuration file for the following section is D:\code\appweb\blackjack_flutter\build\app\intermediates\default_proguard_files\global\proguard-android-optimize.txt-8.7.3
# This is a configuration file for ProGuard.
# http://proguard.sourceforge.net/index.html#manual/usage.html
#
# Starting with version 2.2 of the Android plugin for Gradle, this file is distributed together with
# the plugin and unpacked at build-time. The files in $ANDROID_HOME are no longer maintained and
# will be ignored by new version of the Android plugin for Gradle.

# Optimizations: If you don't want to optimize, use the proguard-android.txt configuration file
# instead of this one, which turns off the optimization flags.
-allowaccessmodification

# Preserve some attributes that may be required for reflection.
-keepattributes AnnotationDefault,
                EnclosingMethod,
                InnerClasses,
                RuntimeVisibleAnnotations,
                RuntimeVisibleParameterAnnotations,
                RuntimeVisibleTypeAnnotations,
                Signature

-keep public class com.google.vending.licensing.ILicensingService
-keep public class com.android.vending.licensing.ILicensingService
-keep public class com.google.android.vending.licensing.ILicensingService
-dontnote com.android.vending.licensing.ILicensingService
-dontnote com.google.vending.licensing.ILicensingService
-dontnote com.google.android.vending.licensing.ILicensingService

# For native methods, see https://www.guardsquare.com/manual/configuration/examples#native
-keepclasseswithmembernames,includedescriptorclasses class * {
    native <methods>;
}

# Keep setters in Views so that animations can still work.
-keepclassmembers public class * extends android.view.View {
    void set*(***);
    *** get*();
}

# We want to keep methods in Activity that could be used in the XML attribute onClick.
-keepclassmembers class * extends android.app.Activity {
    public void *(android.view.View);
}

# For enumeration classes, see https://www.guardsquare.com/manual/configuration/examples#enumerations
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

-keepclassmembers class * implements android.os.Parcelable {
    public static final ** CREATOR;
}

# Preserve annotated Javascript interface methods.
-keepclassmembers class * {
    @android.webkit.JavascriptInterface <methods>;
}

# The support libraries contains references to newer platform versions.
# Don't warn about those in case this app is linking against an older
# platform version. We know about them, and they are safe.
-dontnote android.support.**
-dontnote androidx.**
-dontwarn android.support.**
-dontwarn androidx.**

# Understand the @Keep support annotation.
-keep class android.support.annotation.Keep

-keep @android.support.annotation.Keep class * {*;}

-keepclasseswithmembers class * {
    @android.support.annotation.Keep <methods>;
}

-keepclasseswithmembers class * {
    @android.support.annotation.Keep <fields>;
}

-keepclasseswithmembers class * {
    @android.support.annotation.Keep <init>(...);
}

# These classes are duplicated between android.jar and org.apache.http.legacy.jar.
-dontnote org.apache.http.**
-dontnote android.net.http.**

# These classes are duplicated between android.jar and core-lambda-stubs.jar.
-dontnote java.lang.invoke.**

# End of content from D:\code\appweb\blackjack_flutter\build\app\intermediates\default_proguard_files\global\proguard-android-optimize.txt-8.7.3
# The proguard configuration file for the following section is D:\flutter\flutter\packages\flutter_tools\gradle\flutter_proguard_rules.pro
# Build the ephemeral app in a module project.
# Prevents: Warning: library class <plugin-package> depends on program class io.flutter.plugin.**
# This is due to plugins (libraries) depending on the embedding (the program jar)
-dontwarn io.flutter.plugin.**

# The android.** package is provided by the OS at runtime.
-dontwarn android.**

# In some cases, R8 is incorrectly stripping plugin classes. Keep
# all implementations of FlutterPlugin until we can determine
# why this is the case.
# See https://github.com/flutter/flutter/issues/154580.
-if class * implements io.flutter.embedding.engine.plugins.FlutterPlugin
-keep,allowshrinking,allowobfuscation class <1>

# End of content from D:\flutter\flutter\packages\flutter_tools\gradle\flutter_proguard_rules.pro
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.12\transforms\26c99be856553367d8fad52c95155b00\transformed\jetified-window-1.2.0\proguard.txt
# Copyright (C) 2020 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# A rule that will keep classes that implement SidecarInterface$SidecarCallback if Sidecar seems
# be used. See b/157286362 and b/165268619 for details.
# TODO(b/208543178) investigate how to pass header jar to R8 so we don't need this rule
-if class androidx.window.layout.adapter.sidecar.SidecarCompat {
  public *** setExtensionCallback(androidx.window.layout.adapter.sidecar.ExtensionInterfaceCompat$ExtensionCallbackInterface);
}
-keep class androidx.window.layout.adapter.sidecar.SidecarCompat$TranslatingCallback,
 androidx.window.layout.adapter.sidecar.DistinctElementSidecarCallback {
  public *** onDeviceStateChanged(androidx.window.sidecar.SidecarDeviceState);
  public *** onWindowLayoutChanged(android.os.IBinder, androidx.window.sidecar.SidecarWindowLayoutInfo);
}
# Required for window area API reflection guard
-keep interface androidx.window.area.reflectionguard.* {*;}
# End of content from C:\Users\<USER>\.gradle\caches\8.12\transforms\26c99be856553367d8fad52c95155b00\transformed\jetified-window-1.2.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.12\transforms\fe4c05eaffed710d6c61f97a8b8ac890\transformed\fragment-1.7.1\proguard.txt
# Copyright (C) 2020 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# The default FragmentFactory creates Fragment instances using reflection
-if public class ** extends androidx.fragment.app.Fragment
-keepclasseswithmembers,allowobfuscation public class <1> {
    public <init>();
}

# FragmentTransition will reflectively lookup:
# androidx.transition.FragmentTransitionSupport
# We should ensure that we keep the constructor if the code using this is alive
-if class androidx.fragment.app.FragmentTransition {
   private static androidx.fragment.app.FragmentTransitionImpl resolveSupportImpl();
}
-keep class androidx.transition.FragmentTransitionSupport {
    public <init>();
}

# End of content from C:\Users\<USER>\.gradle\caches\8.12\transforms\fe4c05eaffed710d6c61f97a8b8ac890\transformed\fragment-1.7.1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.12\transforms\c7d3b67c81f17a9d01acb41ef7dc29fd\transformed\lifecycle-viewmodel-2.7.0\proguard.txt
-keepclassmembers,allowobfuscation class * extends androidx.lifecycle.ViewModel {
    <init>();
}

-keepclassmembers,allowobfuscation class * extends androidx.lifecycle.AndroidViewModel {
    <init>(android.app.Application);
}

# End of content from C:\Users\<USER>\.gradle\caches\8.12\transforms\c7d3b67c81f17a9d01acb41ef7dc29fd\transformed\lifecycle-viewmodel-2.7.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.12\transforms\c26e9bd9e94c83e6b65abe3b819ef578\transformed\jetified-lifecycle-process-2.7.0\proguard.txt
# this rule is need to work properly when app is compiled with api 28, see b/142778206
-keepclassmembers class * extends androidx.lifecycle.EmptyActivityLifecycleCallbacks { *; }
# End of content from C:\Users\<USER>\.gradle\caches\8.12\transforms\c26e9bd9e94c83e6b65abe3b819ef578\transformed\jetified-lifecycle-process-2.7.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.12\transforms\274b962bfc0ee3499460b07125b40653\transformed\jetified-savedstate-1.2.1\proguard.txt
# Copyright (C) 2019 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

-keepclassmembers,allowobfuscation class * implements androidx.savedstate.SavedStateRegistry$AutoRecreated {
    <init>();
}

# End of content from C:\Users\<USER>\.gradle\caches\8.12\transforms\274b962bfc0ee3499460b07125b40653\transformed\jetified-savedstate-1.2.1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.12\transforms\fed144bef681fc8fb80134095669b372\transformed\lifecycle-runtime-2.7.0\proguard.txt
-keepattributes AnnotationDefault,
                RuntimeVisibleAnnotations,
                RuntimeVisibleParameterAnnotations,
                RuntimeVisibleTypeAnnotations

-keepclassmembers enum androidx.lifecycle.Lifecycle$Event {
    <fields>;
}

-keep class * implements androidx.lifecycle.GeneratedAdapter {
    <init>(...);
}

-keepclassmembers class ** {
    @androidx.lifecycle.OnLifecycleEvent *;
}

# this rule is need to work properly when app is compiled with api 28, see b/142778206
# Also this rule prevents registerIn from being inlined.
-keepclassmembers class androidx.lifecycle.ReportFragment$LifecycleCallbacks { *; }
# End of content from C:\Users\<USER>\.gradle\caches\8.12\transforms\fed144bef681fc8fb80134095669b372\transformed\lifecycle-runtime-2.7.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.12\transforms\51aab5cd0fa4197e7c50f04a365e85eb\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\proguard.txt
-keepclassmembers,allowobfuscation class * extends androidx.lifecycle.ViewModel {
    <init>(androidx.lifecycle.SavedStateHandle);
}

-keepclassmembers,allowobfuscation class * extends androidx.lifecycle.AndroidViewModel {
    <init>(android.app.Application,androidx.lifecycle.SavedStateHandle);
}

# End of content from C:\Users\<USER>\.gradle\caches\8.12\transforms\51aab5cd0fa4197e7c50f04a365e85eb\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.12\transforms\73aa083afefb941d18007d1b70cec6be\transformed\core-1.13.1\proguard.txt
# Never inline methods, but allow shrinking and obfuscation.
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.core.view.ViewCompat$Api* {
  <methods>;
}
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.core.view.WindowInsetsCompat$*Impl* {
  <methods>;
}
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.core.app.NotificationCompat$*$Api*Impl {
  <methods>;
}
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.core.os.UserHandleCompat$Api*Impl {
  <methods>;
}
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.core.widget.EdgeEffectCompat$Api*Impl {
  <methods>;
}

# End of content from C:\Users\<USER>\.gradle\caches\8.12\transforms\73aa083afefb941d18007d1b70cec6be\transformed\core-1.13.1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.12\transforms\506e321e672d66bb2b4486d2ee8beed8\transformed\jetified-startup-runtime-1.1.1\proguard.txt
# It's important that we preserve initializer names, given they are used in the AndroidManifest.xml.
-keepnames class * extends androidx.startup.Initializer

# These Proguard rules ensures that ComponentInitializers are are neither shrunk nor obfuscated,
# and are a part of the primary dex file. This is because they are discovered and instantiated
# during application startup.
-keep class * extends androidx.startup.Initializer {
    # Keep the public no-argument constructor while allowing other methods to be optimized.
    <init>();
}

-assumenosideeffects class androidx.startup.StartupLogger { public static <methods>; }

# End of content from C:\Users\<USER>\.gradle\caches\8.12\transforms\506e321e672d66bb2b4486d2ee8beed8\transformed\jetified-startup-runtime-1.1.1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.12\transforms\a57044d90e87c68614d167bac9b8275a\transformed\versionedparcelable-1.1.1\proguard.txt
-keep class * implements androidx.versionedparcelable.VersionedParcelable
-keep public class android.support.**Parcelizer { *; }
-keep public class androidx.**Parcelizer { *; }
-keep public class androidx.versionedparcelable.ParcelImpl

# End of content from C:\Users\<USER>\.gradle\caches\8.12\transforms\a57044d90e87c68614d167bac9b8275a\transformed\versionedparcelable-1.1.1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.12\transforms\40dfcd36206381321d61bc4d0a504790\transformed\jetified-core-1.0.0\proguard.txt
# Copyright (C) 2022 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# These interfaces must be kept for the client library to invoke methods in extensions.
-keep interface androidx.window.extensions.core.util.function.Consumer {
  public void accept(***);
}
-keep interface androidx.window.extensions.core.util.function.Predicate {
  public boolean test(***);
}
-keep interface androidx.window.extensions.core.util.function.Function {
  public *** apply(***);
}
# End of content from C:\Users\<USER>\.gradle\caches\8.12\transforms\40dfcd36206381321d61bc4d0a504790\transformed\jetified-core-1.0.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.12\transforms\98c9373b623d8e297e4e7d1066df2e16\transformed\rules\lib\META-INF\proguard\androidx-annotations.pro
-keep,allowobfuscation @interface androidx.annotation.Keep
-keep @androidx.annotation.Keep class * {*;}

-keepclasseswithmembers class * {
    @androidx.annotation.Keep <methods>;
}

-keepclasseswithmembers class * {
    @androidx.annotation.Keep <fields>;
}

-keepclasseswithmembers class * {
    @androidx.annotation.Keep <init>(...);
}

-keepclassmembers,allowobfuscation class * {
  @androidx.annotation.DoNotInline <methods>;
}

# End of content from C:\Users\<USER>\.gradle\caches\8.12\transforms\98c9373b623d8e297e4e7d1066df2e16\transformed\rules\lib\META-INF\proguard\androidx-annotations.pro
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.12\transforms\097391d32240fba671c3719c66dc1873\transformed\rules\lib\META-INF\com.android.tools\r8\coroutines.pro
# When editing this file, update the following files as well:
# - META-INF/proguard/coroutines.pro
# - META-INF/com.android.tools/proguard/coroutines.pro

# Most of volatile fields are updated with AFU and should not be mangled
-keepclassmembers class kotlinx.coroutines.** {
    volatile <fields>;
}

# Same story for the standard library's SafeContinuation that also uses AtomicReferenceFieldUpdater
-keepclassmembers class kotlin.coroutines.SafeContinuation {
    volatile <fields>;
}

# These classes are only required by kotlinx.coroutines.debug.AgentPremain, which is only loaded when
# kotlinx-coroutines-core is used as a Java agent, so these are not needed in contexts where ProGuard is used.
-dontwarn java.lang.instrument.ClassFileTransformer
-dontwarn sun.misc.SignalHandler
-dontwarn java.lang.instrument.Instrumentation
-dontwarn sun.misc.Signal

# Only used in `kotlinx.coroutines.internal.ExceptionsConstructor`.
# The case when it is not available is hidden in a `try`-`catch`, as well as a check for Android.
-dontwarn java.lang.ClassValue

# An annotation used for build tooling, won't be directly accessed.
-dontwarn org.codehaus.mojo.animal_sniffer.IgnoreJRERequirement
# End of content from C:\Users\<USER>\.gradle\caches\8.12\transforms\097391d32240fba671c3719c66dc1873\transformed\rules\lib\META-INF\com.android.tools\r8\coroutines.pro
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.12\transforms\de43e5924c5b2b190b012a3c878e852a\transformed\rules\lib\META-INF\com.android.tools\r8-from-1.6.0\coroutines.pro
# Allow R8 to optimize away the FastServiceLoader.
# Together with ServiceLoader optimization in R8
# this results in direct instantiation when loading Dispatchers.Main
-assumenosideeffects class kotlinx.coroutines.internal.MainDispatcherLoader {
    boolean FAST_SERVICE_LOADER_ENABLED return false;
}

-assumenosideeffects class kotlinx.coroutines.internal.FastServiceLoaderKt {
    boolean ANDROID_DETECTED return true;
}

# Disable support for "Missing Main Dispatcher", since we always have Android main dispatcher
-assumenosideeffects class kotlinx.coroutines.internal.MainDispatchersKt {
    boolean SUPPORT_MISSING return false;
}

# Statically turn off all debugging facilities and assertions
-assumenosideeffects class kotlinx.coroutines.DebugKt {
    boolean getASSERTIONS_ENABLED() return false;
    boolean getDEBUG() return false;
    boolean getRECOVER_STACK_TRACES() return false;
}

# End of content from C:\Users\<USER>\.gradle\caches\8.12\transforms\de43e5924c5b2b190b012a3c878e852a\transformed\rules\lib\META-INF\com.android.tools\r8-from-1.6.0\coroutines.pro
# The proguard configuration file for the following section is <unknown>

# End of content from <unknown>