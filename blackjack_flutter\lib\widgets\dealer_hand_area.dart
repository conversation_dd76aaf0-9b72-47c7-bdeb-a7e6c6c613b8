import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/game_provider.dart';
import '../widgets/animated_card.dart';
import '../widgets/animated_game_message.dart';

class DealerHandArea extends StatelessWidget {
  const DealerHandArea({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<GameProvider>(
      builder: (context, gameProvider, child) {
        return Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // 分数显示
            if (gameProvider.dealerCards.isNotEmpty)
              Padding(
                padding: const EdgeInsets.only(bottom: 8),
                child: Center(
                  child: AnimatedScoreDisplay(
                    score: gameProvider.dealerScore,
                    label: 'DEALER',
                  ),
                ),
              ),
            // 卡牌显示
            Container(
              height: 140,
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: gameProvider.dealerCards.isEmpty
                  ? const SizedBox.shrink()
                  : _buildStackedCards(gameProvider.dealerCards),
            ),
          ],
        );
      },
    );
  }

  Widget _buildStackedCards(List<dynamic> cards) {
    return Consumer<GameProvider>(
      builder: (context, gameProvider, child) {
        // 动态计算卡牌间距：随着牌数增多而缩小
        double getCardSpacing(int cardCount) {
          if (cardCount <= 2) return 45.0;      // 1-2张牌：正常间距
          if (cardCount == 3) return 40.0;      // 3张牌：稍微靠近
          if (cardCount == 4) return 35.0;      // 4张牌：更靠近
          if (cardCount == 5) return 30.0;      // 5张牌：很靠近
          return 25.0;                          // 6张及以上：最紧密
        }

        double spacing = getCardSpacing(cards.length);

        return Center(
          child: SizedBox(
            width: 75 + (cards.length - 1) * spacing, // 动态间距
            height: 110,
            child: Stack(
              children: cards
                  .asMap()
                  .entries
                  .map((entry) => _buildCard(entry.value, entry.key, spacing, gameProvider))
                  .toList(),
            ),
          ),
        );
      },
    );
  }

  Widget _buildCard(dynamic card, int index, double spacing, GameProvider gameProvider) {
    String cardName;
    if (card is String) {
      cardName = card;
    } else {
      // Card对象转换为字符串
      cardName = card.toString();
    }

    // 在游戏进行中隐藏庄家的第二张牌
    bool shouldHide = index == 1 &&
                     gameProvider.gameInProgress &&
                     gameProvider.currentPhase != GamePhase.results &&
                     gameProvider.currentPhase != GamePhase.dealerTurn;

    return AnimatedPositioned(
      duration: const Duration(milliseconds: 300), // 靠紧动画时长
      curve: Curves.easeInOut, // 平滑的动画曲线
      left: index * spacing, // 使用动态间距
      child: AnimatedCard(
        cardName: cardName,
        isHidden: shouldHide,
        animationDelay: Duration(milliseconds: _getCardAnimationDelay(index, true)),
        zIndex: index, // 后面的卡牌层级更高
      ),
    );
  }

  // Calculate animation delay for blackjack-style dealing
  int _getCardAnimationDelay(int cardIndex, bool isDealer) {
    // Professional blackjack dealing pattern:
    // Card 0: Player first card - 0ms
    // Card 1: Dealer first card - 150ms
    // Card 2: Player second card - 300ms
    // Card 3: Dealer second card - 450ms
    // Additional cards: immediate for hit/stand actions

    if (cardIndex == 0) {
      return isDealer ? 150 : 0; // First card
    } else if (cardIndex == 1) {
      return isDealer ? 450 : 300; // Second card
    } else {
      return 0; // Additional cards (hit/stand) - immediate
    }
  }
}
