import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/game_provider.dart';
import '../widgets/animated_card.dart';
import '../widgets/animated_game_message.dart';
import 'split_animation.dart';

class PlayerHandArea extends StatelessWidget {
  const PlayerHandArea({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<GameProvider>(
      builder: (context, gameProvider, child) {
        // 如果是分牌模式，不显示普通玩家手牌
        if (gameProvider.isSplitGame) {
          return const SizedBox.shrink();
        }

        // 如果正在显示分牌动画
        if (gameProvider.showSplitAnimation && gameProvider.playerCards.length >= 2) {
          return SplitAnimation(
            originalCards: gameProvider.playerCards,
            onAnimationComplete: () {
              // 动画完成后的回调已在GameProvider中处理
            },
          );
        }

        return Column(
          children: [
            if (gameProvider.playerCards.isNotEmpty)
              AnimatedScoreDisplay(
                score: gameProvider.playerScore,
                label: 'PLAYER',
              ),
            const SizedBox(height: 10),
            Container(
              height: 140,
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: gameProvider.playerCards.isEmpty
                  ? const SizedBox.shrink()
                  : _buildStackedCards(gameProvider.playerCards),
            ),
          ],
        );
      },
    );
  }

  Widget _buildStackedCards(List<dynamic> cards) {
    // 动态计算卡牌间距：随着牌数增多而缩小
    double getCardSpacing(int cardCount) {
      if (cardCount <= 2) return 45.0;      // 1-2张牌：正常间距
      if (cardCount == 3) return 40.0;      // 3张牌：稍微靠近
      if (cardCount == 4) return 35.0;      // 4张牌：更靠近
      if (cardCount == 5) return 30.0;      // 5张牌：很靠近
      return 25.0;                          // 6张及以上：最紧密
    }

    double spacing = getCardSpacing(cards.length);

    return Center(
      child: SizedBox(
        width: 75 + (cards.length - 1) * spacing, // 动态间距
        height: 110,
        child: Stack(
          children: cards
              .asMap()
              .entries
              .map((entry) => _buildCard(entry.value, entry.key, spacing))
              .toList(),
        ),
      ),
    );
  }

  Widget _buildCard(dynamic card, int index, double spacing) {
    String cardName;
    if (card is String) {
      cardName = card;
    } else {
      // Card对象转换为字符串
      cardName = card.toString();
    }

    return AnimatedPositioned(
      duration: const Duration(milliseconds: 300), // 靠紧动画时长
      curve: Curves.easeInOut, // 平滑的动画曲线
      left: index * spacing, // 使用动态间距
      child: AnimatedCard(
        cardName: cardName,
        animationDelay: Duration(milliseconds: _getCardAnimationDelay(index, false)),
        zIndex: index, // 后面的卡牌层级更高
      ),
    );
  }

  // Calculate animation delay for blackjack-style dealing
  int _getCardAnimationDelay(int cardIndex, bool isDealer) {
    // Professional blackjack dealing pattern:
    // Card 0: Player first card - 0ms
    // Card 1: Dealer first card - 150ms
    // Card 2: Player second card - 300ms
    // Card 3: Dealer second card - 450ms
    // Additional cards: immediate for hit/stand actions

    if (cardIndex == 0) {
      return isDealer ? 150 : 0; // First card
    } else if (cardIndex == 1) {
      return isDealer ? 450 : 300; // Second card
    } else {
      return 0; // Additional cards (hit/stand) - immediate
    }
  }
}
