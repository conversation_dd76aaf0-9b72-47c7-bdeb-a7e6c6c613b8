import 'package:flutter/material.dart';
import '../utils/card_generator.dart';

class AnimatedCard extends StatefulWidget {
  final String cardName;
  final double width;
  final double height;
  final bool isHidden;
  final Duration animationDuration;
  final Duration animationDelay;
  final int zIndex;

  const AnimatedCard({
    super.key,
    required this.cardName,
    this.width = 75,
    this.height = 110,
    this.isHidden = false,
    this.animationDuration = const Duration(milliseconds: 350),
    this.animationDelay = const Duration(milliseconds: 0),
    this.zIndex = 0,
  });

  @override
  State<AnimatedCard> createState() => _AnimatedCardState();
}

class _AnimatedCardState extends State<AnimatedCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  late Animation<double> _opacityAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.3,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeOutQuart, // More professional, less bouncy
    ));

    _opacityAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeIn,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, -2), // Start from higher up (deck position)
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeOutQuart, // Snappier, more professional curve
    ));

    // 延迟启动动画
    Future.delayed(widget.animationDelay, () {
      if (mounted) {
        _controller.forward();
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return SlideTransition(
          position: _slideAnimation,
          child: ScaleTransition(
            scale: _scaleAnimation,
            child: FadeTransition(
              opacity: _opacityAnimation,
              child: Container(
                width: widget.width,
                height: widget.height,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.3 + (widget.zIndex * 0.05)),
                      blurRadius: 12 + (widget.zIndex * 2),
                      offset: Offset(3 + widget.zIndex.toDouble(), 5 + (widget.zIndex * 2)),
                    ),
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 4,
                      offset: const Offset(1, 2),
                    ),
                  ],
                ),
                child: CardGenerator.buildCardPlaceholder(
                  widget.cardName,
                  width: widget.width,
                  height: widget.height,
                  isHidden: widget.isHidden,
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}


