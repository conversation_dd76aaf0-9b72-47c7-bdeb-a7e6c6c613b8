{"buildFiles": ["D:\\flutter\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\code\\appweb\\blackjack_flutter\\build\\.cxx\\Debug\\5b696h97\\x86_64", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\code\\appweb\\blackjack_flutter\\build\\.cxx\\Debug\\5b696h97\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}