import 'package:flutter/material.dart';
import 'animated_card.dart';

class SplitAnimation extends StatefulWidget {
  final List<dynamic> originalCards;
  final VoidCallback onAnimationComplete;

  const SplitAnimation({
    super.key,
    required this.originalCards,
    required this.onAnimationComplete,
  });

  @override
  State<SplitAnimation> createState() => _SplitAnimationState();
}

class _SplitAnimationState extends State<SplitAnimation>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<Offset> _leftCardAnimation;
  late Animation<Offset> _rightCardAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    
    _controller = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    // 左边卡牌向左移动
    _leftCardAnimation = Tween<Offset>(
      begin: Offset.zero,
      end: const Offset(-1.5, 0),
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeOutQuart,
    ));

    // 右边卡牌向右移动
    _rightCardAnimation = Tween<Offset>(
      begin: const Offset(0.6, 0), // 开始位置稍微偏右（层叠效果）
      end: const Offset(1.5, 0),
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeOutQuart,
    ));

    // 缩放动画
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.8,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));

    // 开始动画
    _controller.forward().then((_) {
      widget.onAnimationComplete();
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.originalCards.length < 2) {
      return const SizedBox.shrink();
    }

    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return SizedBox(
          height: 140,
          child: Stack(
            alignment: Alignment.center,
            children: [
              // 第一张卡牌（向左移动）
              SlideTransition(
                position: _leftCardAnimation,
                child: ScaleTransition(
                  scale: _scaleAnimation,
                  child: AnimatedCard(
                    cardName: widget.originalCards[0].toString(),
                    animationDelay: Duration.zero,
                  ),
                ),
              ),
              // 第二张卡牌（向右移动）
              SlideTransition(
                position: _rightCardAnimation,
                child: ScaleTransition(
                  scale: _scaleAnimation,
                  child: AnimatedCard(
                    cardName: widget.originalCards[1].toString(),
                    animationDelay: Duration.zero,
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
