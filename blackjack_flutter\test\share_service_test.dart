import 'package:flutter_test/flutter_test.dart';
import 'package:blackjack_flutter/services/share_service.dart';

void main() {
  group('ShareService Tests', () {
    test('shareGame should not throw exception', () async {
      // 测试分享游戏功能不会抛出异常
      expect(() async => await ShareService.shareGame(), returnsNormally);
    });

    test('shareScore should not throw exception', () async {
      // 测试分享统计数据功能不会抛出异常
      expect(
        () async => await ShareService.shareScore(
          balance: 1500.0,
          gamesPlayed: 10,
          gamesWon: 6,
        ),
        returnsNormally,
      );
    });

    test('shareAchievement should not throw exception', () async {
      // 测试分享成就功能不会抛出异常
      expect(
        () async => await ShareService.shareAchievement(
          achievement: "Won 10 games in a row!",
          balance: 2000.0,
        ),
        returnsNormally,
      );
    });

    test('custom message should be used when provided', () async {
      // 测试自定义消息功能
      const customMessage = "Check out this amazing blackjack game!";
      expect(
        () async => await ShareService.shareGame(customMessage: customMessage),
        returnsNormally,
      );
    });
  });
}
