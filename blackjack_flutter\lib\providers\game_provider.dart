import 'package:flutter/material.dart';
import 'dart:math';
import '../utils/audio_manager.dart';

enum GamePhase { betting, playing, dealerTurn, gameOver, results }

class Card {
  final String suit;
  final String value;
  final String color;

  Card({required this.suit, required this.value, required this.color});

  @override
  String toString() {
    // 将花色符号转换为英文名称
    String suitName;
    switch (suit) {
      case '♠':
        suitName = 'spades';
        break;
      case '♥':
        suitName = 'hearts';
        break;
      case '♦':
        suitName = 'diamonds';
        break;
      case '♣':
        suitName = 'clubs';
        break;
      default:
        suitName = suit.toLowerCase();
    }
    return '${suitName}_${value.toLowerCase()}';
  }
}

class SplitHand {
  List<Card> cards;
  int score;
  double bet;
  bool isBust;
  bool isComplete;
  bool isBlackjack;

  SplitHand({
    required this.cards,
    this.score = 0,
    required this.bet,
    this.isBust = false,
    this.isComplete = false,
    this.isBlackjack = false,
  });
}

class GameProvider extends ChangeNotifier {
  GamePhase _currentPhase = GamePhase.betting;
  double _playerBalance = 1000.0;
  double _currentBet = 0.0;
  double _lastBetAmount = 0.0; // 记住上一局的下注金额
  int _dealerScore = 0;
  int _playerScore = 0;
  int _remainingCards = 312;
  List<Card> _dealerCards = [];
  List<Card> _playerCards = [];
  List<Card> _deck = [];
  String _gameMessage = "Place your bet to start playing";
  bool _gameInProgress = false;
  bool _playerBust = false;
  bool _dealerBust = false;
  bool _playerBlackjack = false;
  bool _dealerBlackjack = false;
  bool _isProcessingAction = false; // 防止连续点击

  // Split related variables
  List<SplitHand> _splitHands = [];
  int _currentHandIndex = 0;
  bool _isSplitGame = false;
  bool _showSplitAnimation = false;

  // Game statistics
  int _gamesPlayed = 0;
  int _gamesWon = 0;
  int _totalWinnings = 0;
  int _totalLosses = 0;

  // 构造函数 - 检查初始余额
  GameProvider() {
    _checkInitialBalance();
  }

  // 检查初始余额，如果为0则给1000筹码
  void _checkInitialBalance() {
    if (_playerBalance <= 0) {
      _playerBalance = 1000.0;
      _gameMessage = "Welcome! You've been given 1000 chips to start playing!";
    }
  }

  GamePhase get currentPhase => _currentPhase;
  double get playerBalance => _playerBalance;
  double get currentBet => _currentBet;
  double get lastBetAmount => _lastBetAmount;
  int get dealerScore => _dealerScore;
  int get playerScore => _playerScore;
  int get remainingCards => _remainingCards;
  List<Card> get dealerCards => _dealerCards;
  List<Card> get playerCards => _playerCards;
  String get gameMessage => _gameMessage;
  bool get gameInProgress => _gameInProgress;
  bool get playerBust => _playerBust;
  bool get dealerBust => _dealerBust;
  bool get playerBlackjack => _playerBlackjack;
  bool get dealerBlackjack => _dealerBlackjack;

  // Statistics getters
  int get gamesPlayed => _gamesPlayed;
  int get gamesWon => _gamesWon;
  int get totalWinnings => _totalWinnings;
  int get totalLosses => _totalLosses;
  double get winRate => _gamesPlayed > 0 ? (_gamesWon / _gamesPlayed) * 100 : 0.0;

  // Split related getters
  List<SplitHand> get splitHands => _splitHands;
  int get currentHandIndex => _currentHandIndex;
  bool get isSplitGame => _isSplitGame;
  bool get showSplitAnimation => _showSplitAnimation;
  SplitHand? get currentSplitHand => _isSplitGame && _currentHandIndex < _splitHands.length
      ? _splitHands[_currentHandIndex] : null;

  // Double Down availability
  bool get canDoubleDown => _currentPhase == GamePhase.playing &&
                           _playerCards.length == 2 &&
                           _currentBet <= _playerBalance;

  // Surrender availability (only on initial 2 cards)
  bool get canSurrender => _currentPhase == GamePhase.playing &&
                          _playerCards.length == 2;

  // Split availability (two cards of same rank)
  bool get canSplit => _currentPhase == GamePhase.playing &&
                      !_isSplitGame &&
                      _playerCards.length == 2 &&
                      _currentBet <= _playerBalance &&
                      _getCardRank(_playerCards[0]) == _getCardRank(_playerCards[1]);

  // Insurance availability (dealer shows Ace)
  bool get canTakeInsurance => _currentPhase == GamePhase.playing &&
                              _playerCards.length == 2 &&
                              _dealerCards.isNotEmpty &&
                              _dealerCards[0].value == 'A' &&
                              _currentBet / 2 <= _playerBalance;

  void placeBet(double amount) {
    // 只在下注阶段允许下注
    if (_currentPhase != GamePhase.betting) return;

    if (_playerBalance >= amount) {
      _currentBet += amount;
      _playerBalance -= amount;

      // 更新游戏消息
      if (_currentBet > 0) {
        _gameMessage = "Bet: ${_currentBet.toStringAsFixed(0)} - Tap DEAL to start!";
      }

      notifyListeners();
    }
    // 移除了余额不足时的游戏结束检查，这应该只在游戏结算后检查
  }

  void clearBet() {
    // 只在下注阶段允许清除下注
    if (_currentPhase != GamePhase.betting) return;

    _playerBalance += _currentBet;
    _currentBet = 0.0;
    _gameMessage = "Place your bet to start playing";
    notifyListeners();
  }

  void doubleBet() {
    if (_playerBalance >= _currentBet) {
      _playerBalance -= _currentBet;
      _currentBet *= 2;
      notifyListeners();
    }
  }

  // 创建牌组
  void _createDeck() {
    _deck.clear();
    const suits = ['♠', '♥', '♦', '♣'];
    const values = ['A', '2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K'];
    const suitColors = {'♠': 'black', '♣': 'black', '♥': 'red', '♦': 'red'};

    // 创建6副牌
    for (int deckNum = 0; deckNum < 6; deckNum++) {
      for (String suit in suits) {
        for (String value in values) {
          _deck.add(Card(
            suit: suit,
            value: value,
            color: suitColors[suit]!,
          ));
        }
      }
    }
    _shuffleDeck();
    _remainingCards = _deck.length;
  }

  // 洗牌
  void _shuffleDeck() {
    final random = Random();
    for (int i = _deck.length - 1; i > 0; i--) {
      int j = random.nextInt(i + 1);
      Card temp = _deck[i];
      _deck[i] = _deck[j];
      _deck[j] = temp;
    }
  }

  // 发一张牌
  Card? _dealCard() {
    if (_deck.isEmpty) {
      _createDeck();
    }
    if (_deck.isNotEmpty) {
      _remainingCards = _deck.length - 1;

      // 播放发牌音效
      AudioManager().playDealSound();

      return _deck.removeLast();
    }
    return null;
  }

  // 计算分数
  int _calculateScore(List<Card> cards) {
    int score = 0;
    int aces = 0;

    for (Card card in cards) {
      if (card.value == 'A') {
        aces++;
        score += 11;
      } else if (['J', 'Q', 'K'].contains(card.value)) {
        score += 10;
      } else {
        score += int.parse(card.value);
      }
    }

    // 处理A的软硬计算
    while (score > 21 && aces > 0) {
      score -= 10;
      aces--;
    }

    return score;
  }

  void startGame() {
    if (_currentBet > 0) {
      // 记录这一局的下注金额，用于下一局自动下注
      _lastBetAmount = _currentBet;

      _currentPhase = GamePhase.playing;
      _gameInProgress = true;

      // 创建新牌组
      if (_deck.isEmpty) {
        _createDeck();
      }

      // 清空手牌
      _dealerCards.clear();
      _playerCards.clear();

      // 重置状态
      _playerBust = false;
      _dealerBust = false;
      _playerBlackjack = false;
      _dealerBlackjack = false;

      // 重置分牌状态
      _splitHands.clear();
      _currentHandIndex = 0;
      _isSplitGame = false;
      _showSplitAnimation = false;

      _gameMessage = "Dealing cards...";
      notifyListeners();

      // 模拟发牌延迟，让动画有时间播放
      _dealInitialCards();
    }
  }

  void _dealInitialCards() {
    // Professional blackjack-style dealing: all 4 cards start animating simultaneously
    // Deal all cards immediately and let animations handle the visual timing

    // Deal all 4 cards at once
    _playerCards.add(_dealCard()!);
    _dealerCards.add(_dealCard()!);
    _playerCards.add(_dealCard()!);
    _dealerCards.add(_dealCard()!);

    // Notify immediately to start all animations
    notifyListeners();

    // Wait for all animations to complete (450ms for last card) then calculate scores
    Future.delayed(const Duration(milliseconds: 800), () {
      // 计算分数
      _playerScore = _calculateScore(_playerCards);
      _dealerScore = _calculateScore([_dealerCards[0]]); // 只显示第一张牌的分数

      // 检查21点
      _playerBlackjack = _playerScore == 21;
      _dealerBlackjack = _calculateScore(_dealerCards) == 21;

      if (_playerBlackjack) {
        _gameMessage = "Blackjack! You got 21!";
        _currentPhase = GamePhase.dealerTurn;
        Future.delayed(const Duration(milliseconds: 1000), () {
          _dealerTurn();
        });
      } else {
        _gameMessage = "Your turn - Hit or Stand?";
      }

      notifyListeners();
    });
  }

  // 玩家要牌
  void hit() {
    if (_currentPhase != GamePhase.playing || _isProcessingAction) return;

    if (_isSplitGame) {
      hitSplitHand();
      return;
    }

    if (_playerBust) return;

    // 设置处理标志，防止连续点击
    _isProcessingAction = true;

    Card? newCard = _dealCard();
    if (newCard != null) {
      // 立即添加卡牌并开始动画，但不更新分数
      _playerCards.add(newCard);
      _gameMessage = "Dealing card...";
      notifyListeners();

      // 等待动画结束（350ms）再更新分数
      Future.delayed(const Duration(milliseconds: 350), () {
        _playerScore = _calculateScore(_playerCards);

        if (_playerScore > 21) {
          _playerBust = true;
          _currentPhase = GamePhase.results;
          _determineWinner();
        } else if (_playerScore == 21) {
          // 立即进入庄家回合，不显示中间消息
          _currentPhase = GamePhase.dealerTurn;
          _dealerTurn();
        } else {
          _gameMessage = "Your turn - Hit or Stand?";
          // 重置处理标志，允许下次操作
          _isProcessingAction = false;
        }

        notifyListeners();
      });
    } else {
      // 如果没有卡牌，重置处理标志
      _isProcessingAction = false;
    }
  }

  // 玩家停牌
  void stand() {
    if (_currentPhase != GamePhase.playing || _isProcessingAction) return;

    if (_isSplitGame) {
      standSplitHand();
      return;
    }

    // 设置处理标志，防止连续点击
    _isProcessingAction = true;

    _currentPhase = GamePhase.dealerTurn;
    // 不显示中间信息，直接进入庄家回合
    _dealerTurn();
  }

  // Double Down - double bet and receive exactly one more card
  void doubleDown() {
    if (canDoubleDown && !_isProcessingAction) {
      // 设置处理标志，防止连续点击
      _isProcessingAction = true;
      // Double the bet
      _playerBalance -= _currentBet;
      _currentBet *= 2;

      _gameMessage = "Double Down! Dealing one card...";
      notifyListeners();

      // Deal exactly one card but don't calculate score yet
      Card? newCard = _dealCard();
      if (newCard != null) {
        _playerCards.add(newCard);
        notifyListeners();

        // Wait for animation to complete (350ms) then calculate score
        Future.delayed(const Duration(milliseconds: 350), () {
          _playerScore = _calculateScore(_playerCards);

          if (_playerScore > 21) {
            _playerBust = true;
            _currentPhase = GamePhase.results;
            _determineWinner();
          } else {
            // 不显示中间信息，直接进入庄家回合
            _currentPhase = GamePhase.dealerTurn;
            Future.delayed(const Duration(milliseconds: 800), () {
              _dealerTurn();
            });
          }

          notifyListeners();
        });
      }
    }
  }

  // Surrender - forfeit half the bet and end the hand
  void surrender() {
    if (canSurrender && !_isProcessingAction) {
      // 设置处理标志，防止连续点击
      _isProcessingAction = true;
      // Return half the bet to player
      _playerBalance += _currentBet / 2;

      _gameMessage = "You surrendered. Half your bet returned.";
      _currentPhase = GamePhase.results;

      // Wait a moment then show continue prompt
      Future.delayed(const Duration(milliseconds: 1500), () {
        _gameMessage = "You surrendered. Half your bet returned.\n\nTap anywhere to continue";
        notifyListeners();

        // 延迟检查余额
        Future.delayed(const Duration(milliseconds: 2000), () {
          _checkGameOverAfterResult();
        });
      });

      notifyListeners();
    }
  }

  // Insurance - bet against dealer blackjack
  void takeInsurance() {
    if (canTakeInsurance) {
      double insuranceBet = _currentBet / 2;
      _playerBalance -= insuranceBet;

      // Check if dealer has blackjack
      bool dealerHasBlackjack = _calculateScore(_dealerCards) == 21;

      if (dealerHasBlackjack) {
        // Insurance pays 2:1
        _playerBalance += insuranceBet * 3; // Original bet + 2:1 payout
        _gameMessage = "Insurance wins! Dealer has Blackjack.";
      } else {
        _gameMessage = "Insurance loses. Continue playing.";
      }

      notifyListeners();

      // Continue with normal game flow
      Future.delayed(const Duration(milliseconds: 2000), () {
        if (dealerHasBlackjack) {
          _currentPhase = GamePhase.results;
          _gameMessage = "Dealer has Blackjack. Insurance saved you!\n\nTap anywhere to continue";
        } else {
          _gameMessage = "Your turn - Hit or Stand?";
        }
        notifyListeners();
      });
    }
  }

  // 庄家回合
  void _dealerTurn() {
    // 不显示中间信息，直接进行庄家逻辑
    // 显示庄家的真实分数
    _dealerScore = _calculateScore(_dealerCards);
    notifyListeners();

    // 稍微延迟让玩家看到翻牌，然后直接进行庄家要牌
    Future.delayed(const Duration(milliseconds: 800), () {
      _dealerHitCards();
    });
  }

  void _dealerHitCards() {
    // 庄家必须在17以下要牌
    if (_dealerScore < 17) {
      Future.delayed(const Duration(milliseconds: 600), () {
        Card? newCard = _dealCard();
        if (newCard != null) {
          _dealerCards.add(newCard);
          // 不显示中间信息，直接更新分数
          notifyListeners();

          Future.delayed(const Duration(milliseconds: 400), () {
            _dealerScore = _calculateScore(_dealerCards);
            notifyListeners();

            // 递归继续要牌
            _dealerHitCards();
          });
        }
      });
    } else {
      // 庄家停牌，结束游戏
      Future.delayed(const Duration(milliseconds: 600), () {
        if (_dealerScore > 21) {
          _dealerBust = true;
        }

        _currentPhase = GamePhase.results;
        _determineWinner();
      });
    }
  }

  // 判断胜负
  void _determineWinner() {
    if (_isSplitGame) {
      _determineSplitWinner();
      return;
    }

    String resultMessage = "";
    bool playerWon = false;
    int winnings = 0;

    // 更新游戏统计
    _gamesPlayed++;

    if (_playerBust) {
      resultMessage = "You lose! You went bust";
      _totalLosses += _currentBet.toInt();
    } else if (_dealerBust) {
      resultMessage = "You win! Dealer went bust";
      winnings = (_currentBet * 2).toInt();
      _playerBalance += _currentBet * 2;
      playerWon = true;
      AudioManager().playWinSound(); // 播放胜利音效
    } else if (_playerBlackjack && !_dealerBlackjack) {
      resultMessage = "Blackjack! You win!";
      winnings = (_currentBet * 2.5).toInt();
      _playerBalance += _currentBet * 2.5;
      playerWon = true;
      AudioManager().playWinSound(); // 播放胜利音效
    } else if (_dealerBlackjack && !_playerBlackjack) {
      resultMessage = "Dealer has Blackjack. You lose";
      _totalLosses += _currentBet.toInt();
    } else if (_playerScore > _dealerScore) {
      resultMessage = "You win!";
      winnings = (_currentBet * 2).toInt();
      _playerBalance += _currentBet * 2;
      playerWon = true;
      AudioManager().playWinSound(); // 播放胜利音效
    } else if (_dealerScore > _playerScore) {
      resultMessage = "Dealer wins";
      _totalLosses += _currentBet.toInt();
    } else {
      resultMessage = "Push! It's a tie";
      _playerBalance += _currentBet; // 退还赌注
      // Push不算胜负，所以不更新胜负统计
      _gamesPlayed--; // 撤销游戏计数增加
    }

    if (playerWon) {
      _gamesWon++;
      _totalWinnings += winnings;
    }

    _gameMessage = "$resultMessage\n\nTap anywhere to continue";
    // 重置处理标志，允许用户点击继续
    _isProcessingAction = false;
    notifyListeners();

    // 延迟检查余额，给用户时间看到结果
    Future.delayed(const Duration(milliseconds: 2000), () {
      _checkGameOverAfterResult();
    });
  }

  void _determineSplitWinner() {
    List<String> handResults = [];
    double totalWinnings = 0;
    int handsWon = 0;
    int handsLost = 0;
    int totalLosses = 0;

    // 更新游戏统计 - 分牌算作一局游戏
    _gamesPlayed++;

    for (int i = 0; i < _splitHands.length; i++) {
      final hand = _splitHands[i];
      String handResult = "";

      if (hand.isBust) {
        handResult = "Hand ${i + 1}: Bust - Lost";
        handsLost++;
        totalLosses += hand.bet.toInt();
      } else if (_dealerBust) {
        handResult = "Hand ${i + 1}: Won (Dealer bust)";
        totalWinnings += hand.bet * 2;
        handsWon++;
      } else if (hand.isBlackjack && !_dealerBlackjack) {
        handResult = "Hand ${i + 1}: Blackjack!";
        totalWinnings += hand.bet * 2.5;
        handsWon++;
      } else if (_dealerBlackjack && !hand.isBlackjack) {
        handResult = "Hand ${i + 1}: Lost (Dealer blackjack)";
        handsLost++;
        totalLosses += hand.bet.toInt();
      } else if (hand.score > _dealerScore) {
        handResult = "Hand ${i + 1}: Won";
        totalWinnings += hand.bet * 2;
        handsWon++;
      } else if (_dealerScore > hand.score) {
        handResult = "Hand ${i + 1}: Lost";
        handsLost++;
        totalLosses += hand.bet.toInt();
      } else {
        handResult = "Hand ${i + 1}: Push";
        totalWinnings += hand.bet; // 退还赌注
      }

      handResults.add(handResult);
    }

    // 更新统计数据
    if (handsWon > handsLost) {
      _gamesWon++;
      _totalWinnings += totalWinnings.toInt();
    } else if (handsLost > handsWon) {
      _totalLosses += totalLosses;
    } else {
      // 平局情况，撤销游戏计数
      _gamesPlayed--;
    }

    _playerBalance += totalWinnings;

    // 如果有任何胜利，播放胜利音效
    if (totalWinnings > 0) {
      AudioManager().playWinSound();
    }

    String resultMessage = "Split Results:\n${handResults.join('\n')}";
    if (totalWinnings > 0) {
      resultMessage += "\n\nTotal winnings: ${totalWinnings.toStringAsFixed(0)}";
    }

    _gameMessage = "$resultMessage\n\nTap anywhere to continue";
    notifyListeners();

    // 延迟检查余额，给用户时间看到结果
    Future.delayed(const Duration(milliseconds: 2000), () {
      _checkGameOverAfterResult();
    });
  }

  // 在游戏结果显示后检查是否游戏结束
  void _checkGameOverAfterResult() {
    // 只有在结果阶段才检查
    if (_currentPhase != GamePhase.results) return;

    // 如果余额不足以进行最小下注（假设最小下注为1），则游戏结束
    if (_playerBalance <= 0) {
      _currentPhase = GamePhase.gameOver;
      _gameMessage = "Game Over! You're out of chips.";
      notifyListeners();
    }
  }



  void continueToNextRound() {
    if (_currentPhase == GamePhase.results) {
      // 在继续下一轮之前检查余额
      if (_playerBalance <= 0) {
        _currentPhase = GamePhase.gameOver;
        _gameMessage = "Game Over! You're out of chips.";
        notifyListeners();
      } else {
        resetGame();
      }
    } else if (_currentPhase == GamePhase.gameOver) {
      resetGame();
    }
  }

  // 重新开始游戏（给玩家1000余额）
  void restartGame() {
    _playerBalance = 1000.0;
    _lastBetAmount = 0.0;
    resetGame();
  }

  // 检查并确保玩家有足够的余额开始游戏
  void ensureMinimumBalance() {
    if (_playerBalance <= 0) {
      _playerBalance = 1000.0;
      _gameMessage = "You've been given 1000 chips to continue playing!";
      notifyListeners();
    }
  }

  // 设置余额（用于从本地存储加载）
  void setBalance(double balance) {
    _playerBalance = balance;
    _checkInitialBalance();
    notifyListeners();
  }

  void resetGame() {
    // 重置游戏状态
    _dealerScore = 0;
    _playerScore = 0;
    _dealerCards.clear();
    _playerCards.clear();
    _gameInProgress = false;
    _playerBust = false;
    _dealerBust = false;
    _playerBlackjack = false;
    _dealerBlackjack = false;
    _isProcessingAction = false; // 重置处理标志

    // Reset split state
    _splitHands.clear();
    _currentHandIndex = 0;
    _isSplitGame = false;
    _showSplitAnimation = false;

    // 检查余额是否足够继续游戏
    if (_playerBalance <= 0) {
      _currentPhase = GamePhase.gameOver;
      _currentBet = 0.0;
      _gameMessage = "Game Over! You're out of chips.";
      notifyListeners();
      return;
    }

    _currentPhase = GamePhase.betting;

    // 自动下注逻辑：只有当上一局有下注且余额足够时才自动下注
    if (_lastBetAmount > 0 && _playerBalance >= _lastBetAmount) {
      _currentBet = _lastBetAmount;
      _playerBalance -= _lastBetAmount;
      _gameMessage = "Auto bet: ${_lastBetAmount.toStringAsFixed(0)} - Add more chips or tap DEAL!";
    } else {
      _currentBet = 0.0;
      _gameMessage = "Place your bet to start playing";
    }

    notifyListeners();
  }

  // Split functionality
  void splitHand() {
    if (!canSplit || _isProcessingAction) return;

    // 设置处理标志，防止连续点击
    _isProcessingAction = true;

    // Deduct bet for second hand
    _playerBalance -= _currentBet;

    // Show split animation first
    _showSplitAnimation = true;
    _gameMessage = "Splitting cards...";
    notifyListeners();

    // Wait for animation to complete
    Future.delayed(const Duration(milliseconds: 800), () {
      _showSplitAnimation = false;

      // Create two split hands from the original two cards
      final firstCard = _playerCards[0];
      final secondCard = _playerCards[1];

      _splitHands = [
        SplitHand(
          cards: [firstCard],
          bet: _currentBet,
          score: _calculateScore([firstCard]),
        ),
        SplitHand(
          cards: [secondCard],
          bet: _currentBet,
          score: _calculateScore([secondCard]),
        ),
      ];

      // Clear original player cards
      _playerCards.clear();
      _playerScore = 0;

      // Set split game state
      _isSplitGame = true;
      _currentHandIndex = 0;

      _gameMessage = "Cards split! Dealing additional cards...";
      notifyListeners();

      // Deal one card to each split hand with animation delay
      Future.delayed(const Duration(milliseconds: 500), () {
        _dealCardToSplitHand(0);

        Future.delayed(const Duration(milliseconds: 300), () {
          _dealCardToSplitHand(1);

          Future.delayed(const Duration(milliseconds: 300), () {
            _gameMessage = "Playing Hand 1 - Hit or Stand?";
            notifyListeners();
          });
        });
      });
    });
  }

  void _dealCardToSplitHand(int handIndex) {
    if (handIndex >= _splitHands.length) return;

    Card? newCard = _dealCard();
    if (newCard != null) {
      _splitHands[handIndex].cards.add(newCard);
      _splitHands[handIndex].score = _calculateScore(_splitHands[handIndex].cards);

      // Check for blackjack on split hand
      if (_splitHands[handIndex].score == 21 && _splitHands[handIndex].cards.length == 2) {
        _splitHands[handIndex].isBlackjack = true;
      }

      notifyListeners();
    }
  }

  void hitSplitHand() {
    if (!_isSplitGame || _currentHandIndex >= _splitHands.length || _isProcessingAction) return;

    final currentHand = _splitHands[_currentHandIndex];
    if (currentHand.isComplete || currentHand.isBust) return;

    // 设置处理标志，防止连续点击
    _isProcessingAction = true;

    Card? newCard = _dealCard();
    if (newCard != null) {
      // 立即添加卡牌并开始动画，但不更新分数
      currentHand.cards.add(newCard);
      _gameMessage = "Dealing card...";
      notifyListeners();

      Future.delayed(const Duration(milliseconds: 350), () {
        currentHand.score = _calculateScore(currentHand.cards);

        if (currentHand.score > 21) {
          currentHand.isBust = true;
          currentHand.isComplete = true;
          _gameMessage = "Hand ${_currentHandIndex + 1} busted!";

          Future.delayed(const Duration(milliseconds: 1000), () {
            _moveToNextSplitHand();
          });
        } else if (currentHand.score == 21) {
          // 21点立即完成这手牌，进入下一手
          currentHand.isComplete = true;
          _moveToNextSplitHand();
        } else {
          _gameMessage = "Hand ${_currentHandIndex + 1} - Hit or Stand?";
          // 重置处理标志，允许下次操作
          _isProcessingAction = false;
        }

        notifyListeners();
      });
    }
  }

  void standSplitHand() {
    if (!_isSplitGame || _currentHandIndex >= _splitHands.length || _isProcessingAction) return;

    // 设置处理标志，防止连续点击
    _isProcessingAction = true;

    _splitHands[_currentHandIndex].isComplete = true;
    _moveToNextSplitHand();
  }

  void _moveToNextSplitHand() {
    _currentHandIndex++;

    if (_currentHandIndex < _splitHands.length) {
      _gameMessage = "Playing Hand ${_currentHandIndex + 1} - Hit or Stand?";
      // 重置处理标志，允许下一手牌的操作
      _isProcessingAction = false;
      notifyListeners();
    } else {
      // All split hands completed, move to dealer turn
      // 不显示中间信息，直接进入庄家回合
      notifyListeners();

      Future.delayed(const Duration(milliseconds: 800), () {
        _currentPhase = GamePhase.dealerTurn;
        _dealerTurn();
      });
    }
  }

  // Helper method to get card rank for split comparison
  String _getCardRank(Card card) {
    return card.value;
  }
}
