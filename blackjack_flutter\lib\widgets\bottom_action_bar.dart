import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/game_provider.dart';

class BottomActionBar extends StatelessWidget {
  const BottomActionBar({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<GameProvider>(
      builder: (context, gameProvider, child) {
        return ClipRect(
          child: SizedBox(
            height: 130,
            child: gameProvider.currentPhase == GamePhase.betting
                ? _buildBettingPhase(context, gameProvider)
                : _buildGamePhase(context, gameProvider),
          ),
        );
      },
    );
  }

  Widget _buildBettingPhase(BuildContext context, GameProvider gameProvider) {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 第一行：余额和筹码居中
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                _buildBalanceDisplay(gameProvider.playerBalance),
                const SizedBox(width: 12),
                _buildChipSelector(gameProvider),
              ],
            ),
            const SizedBox(height: 12), // 增加间距：4 -> 12，让Clear ×2 Done按钮往下移动
            // 第二行：操作按钮居中
            _buildBettingButtons(gameProvider),
          ],
        ),
      ),
    );
  }

  Widget _buildBalanceDisplay(double balance) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.green.withValues(alpha: 0.8),
        borderRadius: BorderRadius.circular(15),
      ),
      child: Text(
        balance.toInt().toString(),
        style: const TextStyle(
          color: Colors.white,
          fontSize: 14,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildChipSelector(GameProvider gameProvider) {
    final chipData = [
      {'value': 1, 'file': 'chips1.png'},
      {'value': 5, 'file': 'chips2.png'},
      {'value': 10, 'file': 'chips3.png'},
      {'value': 25, 'file': 'chips4.png'},
      {'value': 50, 'file': 'chips5.png'},
      {'value': 100, 'file': 'chips6.png'},
      {'value': 250, 'file': 'chips7.png'},
      {'value': 500, 'file': 'chips8.png'},
      {'value': 1000, 'file': 'chips9.png'},
    ];

    return SizedBox(
      height: 42,
      width: 280,
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          children: chipData.map((chip) {
            final chipValue = chip['value'] as int;
            final chipFile = chip['file'] as String;
            return Padding(
              padding: const EdgeInsets.only(right: 8),
              child: GestureDetector(
                onTap: () => gameProvider.placeBet(chipValue.toDouble()),
                child: _buildChip(chipValue, chipFile),
              ),
            );
          }).toList(),
        ),
      ),
    );
  }

  Widget _buildChip(int value, String fileName) {
    // 使用兼容的PNG图片
    String pngFileName = fileName.replaceAll('.svg', '.png');
    String assetPath = 'assets/images/chips/$pngFileName';

    return Container(
      width: 50,
      height: 50,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.2),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(1),
        child: Stack(
          alignment: Alignment.center,
          children: [
            Image.asset(
              assetPath,
              width: 48,
              height: 48,
              fit: BoxFit.contain,
              errorBuilder: (context, error, stackTrace) {
                // 如果PNG加载失败，使用增强的fallback
                return _buildEnhancedChipFallback(value);
              },
            ),
            // 在筹码上显示金额
            Text(
              value.toString(),
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12, // 适当放大：10 -> 12
                fontWeight: FontWeight.bold,
                shadows: [
                  // 优化的黑边效果，不偏移，浅浅的黑边
                  Shadow(
                    color: Colors.black,
                    blurRadius: 1,
                    offset: Offset(0, 0),
                  ),
                  Shadow(
                    color: Colors.black,
                    blurRadius: 0.5,
                    offset: Offset(0.5, 0),
                  ),
                  Shadow(
                    color: Colors.black,
                    blurRadius: 0.5,
                    offset: Offset(-0.5, 0),
                  ),
                  Shadow(
                    color: Colors.black,
                    blurRadius: 0.5,
                    offset: Offset(0, 0.5),
                  ),
                  Shadow(
                    color: Colors.black,
                    blurRadius: 0.5,
                    offset: Offset(0, -0.5),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }



  Widget _buildEnhancedChipFallback(int value) {
    return Container(
      width: 45,
      height: 45,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: RadialGradient(
          center: const Alignment(-0.3, -0.3),
          radius: 0.8,
          colors: _getEnhancedChipColors(value),
          stops: const [0.0, 0.4, 0.7, 1.0],
        ),
        border: Border.all(color: Colors.white, width: 2.5),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.4),
            blurRadius: 6,
            offset: const Offset(2, 3),
          ),
          BoxShadow(
            color: Colors.white.withValues(alpha: 0.2),
            blurRadius: 2,
            offset: const Offset(-1, -1),
          ),
        ],
      ),
      child: Stack(
        children: [
          // 内圈装饰
          Center(
            child: Container(
              width: 35,
              height: 35,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(color: Colors.white.withValues(alpha: 0.3), width: 1),
              ),
            ),
          ),
          // 文字
          Center(
            child: Text(
              '\$$value',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12, // 适当放大：11 -> 12
                fontWeight: FontWeight.bold,
                shadows: [
                  // 优化的黑边效果，不偏移，浅浅的黑边
                  Shadow(
                    color: Colors.black,
                    blurRadius: 1,
                    offset: Offset(0, 0),
                  ),
                  Shadow(
                    color: Colors.black,
                    blurRadius: 0.5,
                    offset: Offset(0.5, 0),
                  ),
                  Shadow(
                    color: Colors.black,
                    blurRadius: 0.5,
                    offset: Offset(-0.5, 0),
                  ),
                  Shadow(
                    color: Colors.black,
                    blurRadius: 0.5,
                    offset: Offset(0, 0.5),
                  ),
                  Shadow(
                    color: Colors.black,
                    blurRadius: 0.5,
                    offset: Offset(0, -0.5),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<Color> _getEnhancedChipColors(int value) {
    switch (value) {
      case 1:
        return [Colors.white, Colors.grey.shade300, Colors.grey.shade600, Colors.grey.shade800];
      case 5:
        return [Colors.red.shade300, Colors.red.shade600, Colors.red.shade800, Colors.red.shade900];
      case 10:
        return [Colors.blue.shade300, Colors.blue.shade600, Colors.blue.shade800, Colors.blue.shade900];
      case 25:
        return [Colors.green.shade300, Colors.green.shade600, Colors.green.shade800, Colors.green.shade900];
      case 50:
        return [Colors.purple.shade300, Colors.purple.shade600, Colors.purple.shade800, Colors.purple.shade900];
      case 100:
        return [Colors.orange.shade300, Colors.orange.shade600, Colors.orange.shade800, Colors.orange.shade900];
      case 250:
        return [Colors.pink.shade300, Colors.pink.shade600, Colors.pink.shade800, Colors.pink.shade900];
      case 500:
        return [Colors.teal.shade300, Colors.teal.shade600, Colors.teal.shade800, Colors.teal.shade900];
      case 1000:
        return [Colors.amber.shade300, Colors.amber.shade600, Colors.amber.shade800, Colors.amber.shade900];
      default:
        return [Colors.grey.shade300, Colors.grey.shade600, Colors.grey.shade800, Colors.grey.shade900];
    }
  }

  Color _getChipColor(int value) {
    switch (value) {
      case 1:
        return Colors.white;
      case 5:
        return Colors.red;
      case 10:
        return Colors.blue;
      case 25:
        return Colors.green;
      case 50:
        return Colors.purple;
      case 100:
        return Colors.orange;
      case 250:
        return Colors.pink;
      case 500:
        return Colors.teal;
      case 1000:
        return Colors.amber;
      default:
        return Colors.grey;
    }
  }



  Widget _buildBettingButtons(GameProvider gameProvider) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        _buildBettingButton('Clear', () => gameProvider.clearBet(), Colors.red),
        const SizedBox(width: 16),
        _buildBettingButton('×2', () => gameProvider.doubleBet(), Colors.orange),
        const SizedBox(width: 16),
        _buildBettingButton('Done', () => gameProvider.startGame(), Colors.green),
      ],
    );
  }

  // 下注阶段按钮 - 文字更大
  Widget _buildBettingButton(String text, VoidCallback onPressed, Color color) {
    return Container(
      width: 45,
      height: 45,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: color.withValues(alpha: 0.8),
        border: Border.all(color: Colors.white.withValues(alpha: 0.5), width: 1.5),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.3),
            blurRadius: 3,
            offset: const Offset(1, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(22.5),
          child: Center(
            child: Text(
              text,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12, // 增大文字大小
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ),
      ),
    );
  }

  // 游戏阶段按钮 - 增大文字，使用赌场风格颜色
  Widget _buildGameButton(String text, VoidCallback onPressed, Color color) {
    return Container(
      width: 45,
      height: 45,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: color.withValues(alpha: 0.9),
        border: Border.all(color: Colors.white.withValues(alpha: 0.7), width: 2),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.4),
            blurRadius: 4,
            offset: const Offset(1, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(22.5),
          child: Center(
            child: Text(
              text,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 10, // 增大游戏按钮文字
                fontWeight: FontWeight.bold,
                shadows: [
                  Shadow(
                    color: Colors.black,
                    offset: Offset(1, 1),
                    blurRadius: 2,
                  ),
                ],
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ),
      ),
    );
  }



  Widget _buildGamePhase(BuildContext context, GameProvider gameProvider) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          if (gameProvider.currentPhase == GamePhase.playing) ...[
            // 分牌模式下的按钮
            if (gameProvider.isSplitGame) ...[
              _buildGameButton('HIT', () => gameProvider.hitSplitHand(), Colors.green.shade600), // 绿色 - 要牌
              const SizedBox(width: 12),
              _buildGameButton('STAND', () => gameProvider.standSplitHand(), Colors.red.shade600), // 红色 - 停牌
            ] else ...[
              // 普通模式下的按钮：分牌 → 双倍 → 要牌 → 停牌 → 投降
              if (gameProvider.canSplit) ...[
                _buildGameButton('SPL', () => gameProvider.splitHand(), Colors.orange.shade600), // 橙色 - 分牌
                const SizedBox(width: 12),
              ],
              if (gameProvider.canDoubleDown) ...[
                _buildGameButton('2X', () => gameProvider.doubleDown(), Colors.blue.shade600), // 蓝色 - 双倍
                const SizedBox(width: 12),
              ],
              _buildGameButton('HIT', () => gameProvider.hit(), Colors.green.shade600), // 绿色 - 要牌
              const SizedBox(width: 12),
              _buildGameButton('STAND', () => gameProvider.stand(), Colors.red.shade600), // 红色 - 停牌
              if (gameProvider.canSurrender) ...[
                const SizedBox(width: 12),
                _buildGameButton('SUR', () => gameProvider.surrender(), Colors.grey.shade600), // 灰色 - 投降
              ],
              if (gameProvider.canTakeInsurance) ...[
                const SizedBox(width: 12),
                _buildGameButton('INS', () => gameProvider.takeInsurance(), Colors.purple.shade600), // 紫色 - 保险
              ],
            ],
          ],
          // 在结果阶段不显示按钮，用户可以点击任意位置继续
        ],
      ),
    );
  }




}
