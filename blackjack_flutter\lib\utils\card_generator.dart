import 'package:flutter/material.dart';

class CardGenerator {
  static const List<String> suits = ['hearts', 'diamonds', 'clubs', 'spades'];
  static const List<String> ranks = ['A', '2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K'];
  
  static List<String> generateDeck() {
    List<String> deck = [];
    for (String suit in suits) {
      for (String rank in ranks) {
        deck.add('${suit}_${rank.toLowerCase()}');
      }
    }
    return deck;
  }
  
  static Widget buildCardPlaceholder(String cardName, {double width = 60, double height = 90, bool isHidden = false}) {
    if (isHidden) {
      return _buildCardBack(width: width, height: height);
    }

    // 暂时直接使用fallback卡牌，跳过SVG加载
    return _buildFallbackCard(cardName, width: width, height: height);
  }



  static Widget _buildFallbackCard(String cardName, {double width = 60, double height = 90}) {
    List<String> parts = cardName.split('_');
    String suit = parts.isNotEmpty ? parts[0] : 'unknown';
    String rank = parts.length > 1 ? parts[1].toUpperCase() : '?';

    // 处理花色符号
    String suitSymbol = _getSuitSymbol(suit);
    Color cardColor = _getSuitColor(suit);
    
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.15),
            blurRadius: 3,
            offset: const Offset(1, 2),
          ),
        ],
      ),
      child: Stack(
        children: [
          // 左上角的数字和花色
          Positioned(
            top: 3,
            left: 3,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  rank,
                  style: TextStyle(
                    color: cardColor,
                    fontSize: 11,
                    fontWeight: FontWeight.bold,
                    height: 1.0,
                  ),
                ),
                Text(
                  suitSymbol,
                  style: TextStyle(
                    color: cardColor,
                    fontSize: 9,
                    fontWeight: FontWeight.bold,
                    height: 1.0,
                  ),
                ),
              ],
            ),
          ),
          // 中央的大花色符号
          Center(
            child: Text(
              suitSymbol,
              style: TextStyle(
                color: cardColor,
                fontSize: 22,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          // 右下角的数字和花色（旋转180度）
          Positioned(
            bottom: 3,
            right: 3,
            child: Transform.rotate(
              angle: 3.14159,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    rank,
                    style: TextStyle(
                      color: cardColor,
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      height: 1.0,
                    ),
                  ),
                  Text(
                    suitSymbol,
                    style: TextStyle(
                      color: cardColor,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                      height: 1.0,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  static Widget _buildCardBack({double width = 60, double height = 90}) {
    return _buildFallbackCardBack(width: width, height: height);
  }

  static Widget _buildFallbackCardBack({double width = 60, double height = 90}) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Color(0xFF1e40af), Color(0xFF3b82f6)],
        ),
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.15),
            blurRadius: 3,
            offset: const Offset(1, 2),
          ),
        ],
      ),
      child: Center(
        child: Container(
          width: 35,
          height: 35,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(4),
            border: Border.all(color: Colors.white.withValues(alpha: 0.3), width: 1.5),
          ),
          child: const Center(
            child: Text(
              '♠♥♦♣',
              style: TextStyle(
                color: Colors.white,
                fontSize: 10,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
      ),
    );
  }

  static String _getSuitSymbol(String suit) {
    switch (suit) {
      case '♥':
      case 'hearts':
        return '♥';
      case '♦':
      case 'diamonds':
        return '♦';
      case '♣':
      case 'clubs':
        return '♣';
      case '♠':
      case 'spades':
        return '♠';
      default:
        return '?';
    }
  }

  static Color _getSuitColor(String suit) {
    switch (suit) {
      case '♥':
      case '♦':
      case 'hearts':
      case 'diamonds':
        return Colors.red;
      case '♣':
      case '♠':
      case 'clubs':
      case 'spades':
        return Colors.black;
      default:
        return Colors.black;
    }
  }


}
