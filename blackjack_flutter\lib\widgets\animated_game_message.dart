import 'package:flutter/material.dart';

class AnimatedGameMessage extends StatelessWidget {
  final String message;
  final Duration animationDuration;

  const AnimatedGameMessage({
    super.key,
    required this.message,
    this.animationDuration = const Duration(milliseconds: 500),
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.8),
        borderRadius: BorderRadius.circular(25),
        border: Border.all(color: Colors.white.withValues(alpha: 0.3), width: 1),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.5),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Text(
        message,
        style: const TextStyle(
          color: Colors.white,
          fontSize: 16,
          fontWeight: FontWeight.bold,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }
}



// 专门用于显示分数的组件
class AnimatedScoreDisplay extends StatelessWidget {
  final int score;
  final String label;
  final Color? textColor;
  final double fontSize;
  final bool isBust;
  final bool isBlackjack;

  const AnimatedScoreDisplay({
    super.key,
    required this.score,
    required this.label,
    this.textColor = Colors.white,
    this.fontSize = 20,
    this.isBust = false,
    this.isBlackjack = false,
  });

  @override
  Widget build(BuildContext context) {
    Color displayColor = textColor ?? Colors.white;

    if (isBust || score > 21) {
      displayColor = Colors.red;
    } else if (isBlackjack || score == 21) {
      displayColor = Colors.amber;
    }

    String displayText = label.isNotEmpty ? '$label: $score' : '$score';

    return Text(
      displayText,
      style: TextStyle(
        color: displayColor,
        fontSize: fontSize,
        fontWeight: FontWeight.bold,
        shadows: const [
          Shadow(
            color: Colors.black,
            blurRadius: 2,
            offset: Offset(1, 1),
          ),
        ],
      ),
    );
  }
}


